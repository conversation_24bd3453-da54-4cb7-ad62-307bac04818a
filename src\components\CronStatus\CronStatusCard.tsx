import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { Clock, Play, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import { getRelativeTime, formatNumber } from '@/utils/formatters';

/**
 * 定时任务状态接口
 */
interface CronStatus {
  enabled: boolean;
  config: {
    enabled: boolean;
    batchSize: number;
    maxRetries: number;
    cacheTtl: number;
    logLevel: string;
  };
  cacheStats: {
    gets: number;
    sets: number;
    deletes: number;
    hits: number;
    misses: number;
  };
  lastExecution?: string;
}

/**
 * 任务执行结果接口
 */
interface TaskResult {
  success: boolean;
  processed: number;
  errors: number;
  duration: number;
  details: {
    successful: string[];
    failed: Array<{ code: string; error: string }>;
  };
}

/**
 * API函数
 */
const cronApi = {
  async getStatus(): Promise<CronStatus> {
    const response = await fetch('/api/cron/status');
    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || 'Failed to get cron status');
    }
    return data.data;
  },

  async triggerTask(): Promise<TaskResult> {
    const response = await fetch('/api/cron/trigger', { method: 'POST' });
    const data = await response.json();
    if (!data.success) {
      throw new Error(data.message || 'Failed to trigger task');
    }
    return data.data;
  },
};

interface CronStatusCardProps {
  className?: string;
}

/**
 * 定时任务状态卡片组件
 */
export const CronStatusCard: React.FC<CronStatusCardProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const queryClient = useQueryClient();

  // 获取定时任务状态
  const {
    data: status,
    isLoading,
    error,
    refetch,
  } = useQuery('cronStatus', cronApi.getStatus, {
    refetchInterval: 30000, // 30秒刷新一次
    refetchOnWindowFocus: false,
  });

  // 手动触发任务
  const triggerMutation = useMutation(cronApi.triggerTask, {
    onSuccess: () => {
      queryClient.invalidateQueries('cronStatus');
      queryClient.invalidateQueries(['stockData']); // 刷新股票数据
    },
  });

  // 计算缓存命中率
  const hitRate = status?.cacheStats 
    ? status.cacheStats.hits + status.cacheStats.misses > 0
      ? (status.cacheStats.hits / (status.cacheStats.hits + status.cacheStats.misses) * 100).toFixed(1)
      : '0.0'
    : '0.0';

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        <div className="flex items-center gap-3">
          {/* 加载动画 - 已隐藏 */}
          {/* <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-600"></div> */}
          <span className="text-gray-600">加载定时任务状态...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        <div className="flex items-center gap-3 text-red-600">
          <XCircle className="w-5 h-5" />
          <span>无法获取定时任务状态</span>
          <button
            onClick={() => refetch()}
            className="ml-auto p-1 hover:bg-red-50 rounded"
            title="重试"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border ${className}`}>
      {/* 头部 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${status?.enabled ? 'bg-green-100' : 'bg-gray-100'}`}>
              <Clock className={`w-5 h-5 ${status?.enabled ? 'text-green-600' : 'text-gray-500'}`} />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">定时任务</h3>
              <p className="text-sm text-gray-500">
                {status?.enabled ? '运行中' : '已停用'} • 
                {status?.lastExecution 
                  ? `上次执行: ${getRelativeTime(status.lastExecution)}`
                  : '未执行过'
                }
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* 手动触发按钮 */}
            <button
              onClick={() => triggerMutation.mutate()}
              disabled={triggerMutation.isLoading || !status?.enabled}
              className="flex items-center gap-2 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              title="手动触发任务"
            >
              {triggerMutation.isLoading ? (
                /* 触发动画 - 已隐藏 */
                /* <RefreshCw className="w-4 h-4 animate-spin" /> */
                <span className="text-xs">执行中</span>
              ) : (
                <Play className="w-4 h-4" />
              )}
              触发
            </button>

            {/* 展开/收起按钮 */}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
              title={isExpanded ? '收起详情' : '展开详情'}
            >
              <RefreshCw className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {status?.config.batchSize || 0}
            </div>
            <div className="text-xs text-gray-500">批处理大小</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {status?.config.cacheTtl || 0}s
            </div>
            <div className="text-xs text-gray-500">缓存时间</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-semibold text-green-600">
              {hitRate}%
            </div>
            <div className="text-xs text-gray-500">缓存命中率</div>
          </div>
          
          <div className="text-center">
            <div className="text-lg font-semibold text-gray-900">
              {formatNumber(status?.cacheStats.gets || 0, 0)}
            </div>
            <div className="text-xs text-gray-500">缓存请求</div>
          </div>
        </div>
      </div>

      {/* 详细信息 */}
      {isExpanded && (
        <div className="border-t bg-gray-50 p-4">
          <div className="space-y-4">
            {/* 配置信息 */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">配置信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">最大重试次数:</span>
                  <span className="font-medium">{status?.config.maxRetries}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">日志级别:</span>
                  <span className="font-medium">{status?.config.logLevel}</span>
                </div>
              </div>
            </div>

            {/* 缓存统计 */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">缓存统计</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">命中:</span>
                  <span className="font-medium text-green-600">
                    {formatNumber(status?.cacheStats.hits || 0, 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">未命中:</span>
                  <span className="font-medium text-red-600">
                    {formatNumber(status?.cacheStats.misses || 0, 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">写入:</span>
                  <span className="font-medium text-blue-600">
                    {formatNumber(status?.cacheStats.sets || 0, 0)}
                  </span>
                </div>
              </div>
            </div>

            {/* 任务执行结果 */}
            {triggerMutation.data && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">最近执行结果</h4>
                <div className="bg-white rounded border p-3">
                  <div className="flex items-center gap-2 mb-2">
                    {triggerMutation.data.success ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <XCircle className="w-4 h-4 text-red-500" />
                    )}
                    <span className="text-sm font-medium">
                      {triggerMutation.data.success ? '执行成功' : '执行失败'}
                    </span>
                    <span className="text-xs text-gray-500">
                      耗时 {triggerMutation.data.duration}ms
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    处理: {triggerMutation.data.processed} 个股票，
                    错误: {triggerMutation.data.errors} 个
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
