import React from 'react';
import { DataDisplay } from '@/components';
import { useStockList } from '@/hooks/useStockData';
import { BarChart3, TrendingUp, AlertCircle } from 'lucide-react';

interface MainContentProps {
  /** 选中的股票代码 */
  selectedStock: string | null;
  /** 自定义类名 */
  className?: string;
}

/**
 * 主内容区组件
 */
export const MainContent: React.FC<MainContentProps> = ({
  selectedStock,
  className = '',
}) => {
  const { stocks, isLoading: stocksLoading, error: stocksError } = useStockList();

  // 渲染空状态
  const renderEmptyState = () => {
    if (stocksLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center">
          {/* 加载动画 - 已隐藏 */}
          {/* <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mb-4"></div> */}
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            正在加载股票列表...
          </h3>
          <p className="text-gray-500">
            请稍候，正在获取您的股票监控列表
          </p>
        </div>
      );
    }

    if (stocksError) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center">
          <div className="text-red-500 mb-4">
            <AlertCircle className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            加载失败
          </h3>
          <p className="text-gray-500 mb-4">
            无法加载股票列表: {stocksError.message}
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="btn btn-primary"
          >
            重新加载
          </button>
        </div>
      );
    }

    if (stocks.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center">
          <div className="text-gray-400 mb-6">
            <TrendingUp className="w-20 h-20 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-3">
            开始监控股票资金流向
          </h3>
          <p className="text-gray-500 mb-6 max-w-md">
            您还没有添加任何股票到监控列表。请在左侧添加股票代码，开始监控资金流向数据。
          </p>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <span>支持沪深两市股票代码</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <span>实时资金流向数据</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
              <span>专业图表分析</span>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        <div className="text-gray-400 mb-6">
          <BarChart3 className="w-20 h-20 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-3">
          选择股票查看数据
        </h3>
        <p className="text-gray-500 mb-4 max-w-md">
          您已添加 {stocks.length} 只股票到监控列表。请在左侧选择一只股票，查看详细的资金流向数据和图表分析。
        </p>
        <div className="text-sm text-gray-600">
          <p>监控列表中的股票:</p>
          <div className="mt-2 flex flex-wrap gap-2 justify-center">
            {stocks.slice(0, 5).map((stock) => (
              <span 
                key={stock.code}
                className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
              >
                {stock.name} ({stock.code})
              </span>
            ))}
            {stocks.length > 5 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                +{stocks.length - 5} 更多
              </span>
            )}
          </div>
        </div>
      </div>
    );
  };

  // 检查是否为小窗口模式（通过className判断）
  const isCompactMode = className.includes('h-full');

  return (
    <main className={`flex-1 ${isCompactMode ? 'overflow-hidden' : 'overflow-auto'} ${className}`}>
      <div className={isCompactMode ? 'p-2 h-full' : 'p-4 lg:p-6'}>
        {selectedStock ? (
          <>
            {/* 面包屑导航 - 小窗口模式下隐藏 */}
            {!isCompactMode && (
              <div className="mb-6">
                <nav className="flex items-center space-x-2 text-sm text-gray-500">
                  <span>股票监控</span>
                  <span>/</span>
                  <span className="text-gray-900 font-medium">
                    {stocks.find(s => s.code === selectedStock)?.name || selectedStock}
                  </span>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {selectedStock}
                  </span>
                </nav>
              </div>
            )}

            {/* 数据展示组件 */}
            <DataDisplay
              stockCode={selectedStock}
              limit={isCompactMode ? 20 : 240}
              className={isCompactMode ? 'compact-mode' : ''}
            />
          </>
        ) : (
          !isCompactMode && (
            <div className="bg-white rounded-lg border">
              {renderEmptyState()}
            </div>
          )
        )}
      </div>
    </main>
  );
};
